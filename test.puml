@startgantt
' 设计思路：
' 1. 项目按核心流程（设计->测试）和并行工作（后端开发）进行逻辑划分。
' 2. 使用别名 [PD] 和 [PT] 简化依赖声明。
' 3. 通过颜色区分不同类型的工作流，增强可读性。
' 4. 将里程碑放在关键路径的末端，视觉上明确项目目标。

title 项目开发计划甘特图

-- 设计与测试 --
[原型设计] as [PD] requires 15 days
[原型设计] is colored in Lavender/LightBlue
[原型测试] as [PT] requires 10 days
[原型测试] is colored in Coral/Green

-- 并行开发 --
[后端开发] requires 20 days
[后端开发] is colored in LightSkyBlue

-- 依赖与约束 --
[PT] starts at [PD]'s end
[后端开发] starts at [PD]'s start + 5 days

-- 里程碑 --
[公测版发布] happens at [PT]'s end

@endgantt