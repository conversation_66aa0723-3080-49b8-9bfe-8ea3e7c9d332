import networkx as nx
import matplotlib.pyplot as plt
import matplotlib.patches as mpatches
from matplotlib.patches import FancyArrowPatch

# 设置中文支持
plt.rcParams['font.sans-serif'] = ['SimHei']  # 使用黑体
plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题

# 创建有向图
G = nx.DiGraph()

# 添加节点及其属性（任务、时间、日期、负责人、泳道）
tasks = {
    "001": {"label": "提单", "duration": 1, "start": "2025 Aug 14", "end": "2025 Aug 15", "responsible": "Responsible", "lane": "订单确认与规划"},
    "002": {"label": "订单录入", "duration": 1, "start": "2025 Aug 14", "end": "2025 Aug 15", "responsible": "Responsible", "lane": "订单确认与规划"},
    "003": {"label": "编制订单资料", "duration": 1, "start": "2025 Aug 14", "end": "2025 Aug 15", "responsible": "Responsible", "lane": "订单确认与规划"},
    "004": {"label": "产能规划", "duration": 1, "start": "2025 Aug 14", "end": "2025 Aug 15", "responsible": "Responsible", "lane": "订单确认与规划"},
    "005": {"label": "编制物料采购计划", "duration": 2, "start": "2025 Aug 15", "end": "2025 Aug 16", "responsible": "Responsible", "lane": "物料准备"},
    "006": {"label": "原材料流转", "duration": 1, "start": "2025 Aug 16", "end": "2025 Aug 17", "responsible": "Responsible", "lane": "物料准备"},
    "007": {"label": "原材料检验", "duration": 1, "start": "2025 Aug 17", "end": "2025 Aug 18", "responsible": "Responsible", "lane": "物料准备"},
    "008": {"label": "主计划(MPS)", "duration": 1, "start": "2025 Aug 18", "end": "2025 Aug 19", "responsible": "Responsible", "lane": "物料准备"},
    "009": {"label": "实施期(APS)", "duration": 1, "start": "2025 Aug 19", "end": "2025 Aug 20", "responsible": "Responsible", "lane": "生产制造"},
    "010": {"label": "产前会议", "duration": 1, "start": "2025 Aug 20", "end": "2025 Aug 21", "responsible": "Responsible", "lane": "生产制造"},
    "011": {"label": "大货生产", "duration": 1, "start": "2025 Aug 21", "end": "2025 Aug 22", "responsible": "Responsible", "lane": "生产制造"},
    "012": {"label": "生产过程质量检验", "duration": 1, "start": "2025 Aug 22", "end": "2025 Aug 23", "responsible": "Responsible", "lane": "生产制造"},
    "013": {"label": "成品检验", "duration": 1, "start": "2025 Aug 23", "end": "2025 Aug 24", "responsible": "Responsible", "lane": "成品确认"},
    "014": {"label": "成品入库", "duration": 1, "start": "2025 Aug 24", "end": "2025 Aug 25", "responsible": "Responsible", "lane": "成品确认"},
    "015": {"label": "客户发货", "duration": 1, "start": "2025 Aug 25", "end": "2025 Aug 26", "responsible": "Responsible", "lane": "成品确认"},
    "016": {"label": "成品物流管理", "duration": 1, "start": "2025 Aug 26", "end": "2025 Aug 27", "responsible": "Responsible", "lane": "交付与结算"},
    "017": {"label": "装运准备", "duration": 1, "start": "2025 Aug 27", "end": "2025 Aug 28", "responsible": "Responsible", "lane": "交付与结算"},
    "018": {"label": "收款", "duration": 1, "start": "2025 Aug 28", "end": "2025 Aug 29", "responsible": "Responsible", "lane": "交付与结算"}
}

# 添加节点到图
for task_id, attrs in tasks.items():
    G.add_node(task_id, **attrs)

# 添加边（任务依赖关系，添加从 015 到 001 的折弯边）
edges = [
    ("001", "002"), ("002", "003"), ("003", "004"),
    ("004", "005"), ("005", "006"), ("006", "007"), ("007", "008"),
    ("008", "009"), ("009", "010"), ("010", "011"), ("011", "012"),
    ("012", "013"), ("013", "014"), ("014", "015"),
    ("015", "016"), ("016", "017"), ("017", "018"),
    ("015", "001")  # 添加从最右侧到最左侧的折弯边
]
G.add_edges_from(edges)

# 定义泳道和颜色映射
lanes = ["订单确认与规划", "物料准备", "生产制造", "成品确认", "交付与结算"]
lane_colors = {"订单确认与规划": "lightgreen", "物料准备": "lightblue", "生产制造": "orange", "成品确认": "cyan", "交付与结算": "lightyellow"}

# 自定义布局（按泳道分层）
pos = {}
for i, lane in enumerate(lanes):
    lane_nodes = [n for n in G.nodes if G.nodes[n]["lane"] == lane]
    for j, node in enumerate(lane_nodes):
        pos[node] = (j * 2, -i * 2)  # x 按节点顺序排列，y 按泳道分层

# 绘制 PERT 图
plt.figure(figsize=(15, 10))

# 自定义节点形状（带圆角矩形）
for node, (x, y) in pos.items():
    lane = tasks[node]["lane"]
    color = lane_colors[lane]
    rect = mpatches.FancyBboxPatch((x - 0.8, y - 0.4), 1.6, 0.8, boxstyle="round,pad=0.1", fc=color, ec="black", lw=1)
    plt.gca().add_patch(rect)
    plt.text(x, y, f"{node}\n{tasks[node]['label']}\n{tasks[node]['duration']} days\n{tasks[node]['start']} - {tasks[node]['end']}", 
             ha="center", va="center", fontsize=8)

# 绘制边（自定义折弯边）
for u, v in edges:
    if u == "015" and v == "001":  # 折弯边
        x1, y1 = pos[u]
        x2, y2 = pos[v]
        arrow = FancyArrowPatch((x1 + 0.8, y1), (x2 - 0.8, y2), 
                               connectionstyle=f"arc3,rad=0.7", 
                               arrowstyle="->", color="blue", lw=1,
                               path_xy=[(x1 + 0.8, y1), (x1 + 3, y1), (x2 - 3, y2), (x2 - 0.8, y2)])
        plt.gca().add_patch(arrow)
    else:
        nx.draw_networkx_edges(G, pos, edgelist=[(u, v)], edge_color='blue', arrows=True, ax=plt.gca())

# 添加泳道标签和分隔线
for i, lane in enumerate(lanes):
    plt.text(-1, -i * 2, lane, fontsize=12, ha="right", va="center")
    if i < len(lanes) - 1:
        plt.axhline(y=-i * 2 - 1, color='gray', linestyle='--', linewidth=1)

plt.title("PERT 图 - 泳道样式")
plt.axis("off")
plt.show()