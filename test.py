import networkx as nx
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
import matplotlib.patches as patches
from matplotlib.patches import FancyBboxPatch

# 设置中文支持
plt.rcParams['font.sans-serif'] = ['SimHei']  # 使用黑体
plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题

# 创建有向图
G = nx.DiGraph()

# 添加节点及其属性（任务、时间、日期、负责人、泳道）
tasks = {
    "001": {"label": "提单", "duration": 1, "start": "2025 Aug 14", "end": "2025 Aug 15", "responsible": "Responsible", "lane": "订单确认与规划"},
    "002": {"label": "订单录入", "duration": 1, "start": "2025 Aug 14", "end": "2025 Aug 15", "responsible": "Responsible", "lane": "订单确认与规划"},
    "003": {"label": "编制订单资料", "duration": 1, "start": "2025 Aug 14", "end": "2025 Aug 15", "responsible": "Responsible", "lane": "订单确认与规划"},
    "004": {"label": "产能规划", "duration": 1, "start": "2025 Aug 14", "end": "2025 Aug 15", "responsible": "Responsible", "lane": "订单确认与规划"},
    "005": {"label": "编制物料采购计划", "duration": 2, "start": "2025 Aug 15", "end": "2025 Aug 16", "responsible": "Responsible", "lane": "物料准备"},
    "006": {"label": "原材料流转", "duration": 1, "start": "2025 Aug 16", "end": "2025 Aug 17", "responsible": "Responsible", "lane": "物料准备"},
    "007": {"label": "原材料检验", "duration": 1, "start": "2025 Aug 17", "end": "2025 Aug 18", "responsible": "Responsible", "lane": "物料准备"},
    "008": {"label": "主计划(MPS)", "duration": 1, "start": "2025 Aug 18", "end": "2025 Aug 19", "responsible": "Responsible", "lane": "物料准备"},
    "009": {"label": "实施期(APS)", "duration": 1, "start": "2025 Aug 19", "end": "2025 Aug 20", "responsible": "Responsible", "lane": "生产制造"},
    "010": {"label": "产前会议", "duration": 1, "start": "2025 Aug 20", "end": "2025 Aug 21", "responsible": "Responsible", "lane": "生产制造"},
    "011": {"label": "大货生产", "duration": 1, "start": "2025 Aug 21", "end": "2025 Aug 22", "responsible": "Responsible", "lane": "生产制造"},
    "012": {"label": "生产过程质量检验", "duration": 1, "start": "2025 Aug 22", "end": "2025 Aug 23", "responsible": "Responsible", "lane": "生产制造"},
    "013": {"label": "成品检验", "duration": 1, "start": "2025 Aug 23", "end": "2025 Aug 24", "responsible": "Responsible", "lane": "成品确认"},
    "014": {"label": "成品入库", "duration": 1, "start": "2025 Aug 24", "end": "2025 Aug 25", "responsible": "Responsible", "lane": "成品确认"},
    "015": {"label": "客户发货", "duration": 1, "start": "2025 Aug 25", "end": "2025 Aug 26", "responsible": "Responsible", "lane": "成品确认"},
    "016": {"label": "成品物流管理", "duration": 1, "start": "2025 Aug 26", "end": "2025 Aug 27", "responsible": "Responsible", "lane": "交付与结算"},
    "017": {"label": "装运准备", "duration": 1, "start": "2025 Aug 27", "end": "2025 Aug 28", "responsible": "Responsible", "lane": "交付与结算"},
    "018": {"label": "收款", "duration": 1, "start": "2025 Aug 28", "end": "2025 Aug 29", "responsible": "Responsible", "lane": "交付与结算"}
}

# 添加节点到图
for task_id, attrs in tasks.items():
    G.add_node(task_id, **attrs)

# 添加边（任务依赖关系）
edges = [
    ("001", "002"), ("002", "003"), ("003", "004"),
    ("004", "005"), ("005", "006"), ("006", "007"), ("007", "008"),
    ("008", "009"), ("009", "010"), ("010", "011"), ("011", "012"),
    ("012", "013"), ("013", "014"), ("014", "015"),
    ("015", "016"), ("016", "017"), ("017", "018")
]
G.add_edges_from(edges)

# 定义跨泳道的边（需要折弯效果）
cross_lane_edges = [("004", "005"), ("008", "009"), ("012", "013"), ("015", "016")]
# 普通边（同泳道内的连接）
normal_edges = [edge for edge in edges if edge not in cross_lane_edges]

# 定义泳道和颜色映射
lanes = ["订单确认与规划", "物料准备", "生产制造", "成品确认", "交付与结算"]
lane_colors = {"订单确认与规划": "lightgreen", "物料准备": "lightblue", "生产制造": "orange", "成品确认": "cyan", "交付与结算": "lightyellow"}

# 自定义布局（按泳道分层）
pos = {}
for i, lane in enumerate(lanes):
    lane_nodes = [n for n in G.nodes if G.nodes[n]["lane"] == lane]
    for j, node in enumerate(lane_nodes):
        pos[node] = (j * 2, -i * 2)  # x 按节点顺序排列，y 按泳道分层

# 绘制 PERT 图
plt.figure(figsize=(15, 10))
ax = plt.gca()

# 绘制自定义圆角矩形节点
for node in G.nodes:
    x, y = pos[node]
    color = lane_colors[tasks[node]["lane"]]

    # 创建圆角矩形
    rect = FancyBboxPatch((x-0.8, y-0.4), 1.6, 0.8,
                         boxstyle="round,pad=0.1",
                         facecolor=color,
                         edgecolor='black',
                         linewidth=1.5)
    ax.add_patch(rect)

    # 添加节点标签（包含任务ID）
    label_text = f"{node}\n{tasks[node]['label']}\n{tasks[node]['duration']} days\n{tasks[node]['start']} - {tasks[node]['end']}"
    plt.text(x, y, label_text, ha='center', va='center', fontsize=7, weight='bold')

# 绘制普通蓝色箭头边（同泳道内的连接）
nx.draw_networkx_edges(G, pos, edgelist=normal_edges, arrows=True, edge_color='blue', arrowsize=20, arrowstyle='->', width=2)

# 绘制跨泳道的折弯连接线
for start_node, end_node in cross_lane_edges:
    start_pos = pos[start_node]
    end_pos = pos[end_node]

    # 计算节点边缘的连接点
    # 起始节点的底部边缘
    start_x = start_pos[0]
    start_y = start_pos[1] - 0.4  # 节点底部

    # 目标节点的顶部边缘
    end_x = end_pos[0]
    end_y = end_pos[1] + 0.4  # 节点顶部

    # 计算折弯点
    mid_y = start_y - 0.5  # 在起始节点下方

    # 绘制三段式折弯线
    # 第一段：从起始节点底部向下
    plt.annotate('', xy=(start_x, mid_y),
                xytext=(start_x, start_y),
                arrowprops=dict(arrowstyle='-', color='blue', lw=2))

    # 第二段：水平线段
    plt.annotate('', xy=(end_x, mid_y),
                xytext=(start_x, mid_y),
                arrowprops=dict(arrowstyle='-', color='blue', lw=2))

    # 第三段：向上到目标节点（带箭头）
    plt.annotate('', xy=(end_x, end_y),
                xytext=(end_x, mid_y),
                arrowprops=dict(arrowstyle='->', color='blue', lw=2))

# 添加泳道标签
for i, lane in enumerate(lanes):
    plt.text(-1, -i * 2, lane, fontsize=12, ha="right", va="center")
    if i < len(lanes) - 1:  # 在每个泳道之间添加分隔线
        plt.axhline(y=-i * 2 - 1, color='gray', linestyle='--', linewidth=1)

plt.title("PERT 图 - 泳道样式")
plt.axis("off")
plt.tight_layout()
plt.savefig("pert_diagram.png", dpi=300, bbox_inches='tight')
print("PERT图已保存为 pert_diagram.png")
plt.show()